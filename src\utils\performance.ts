/**
 * Performance optimization utilities for improving Core Web Vitals
 * Focuses on reducing CLS, improving LCP, and optimizing FID
 */

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle function for scroll and resize events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Optimize animations for better performance
export function optimizeAnimation(element: HTMLElement, animation: Keyframe[] | PropertyIndexedKeyframes, options?: KeyframeAnimationOptions) {
  // Check if user prefers reduced motion
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    return null;
  }

  // Use Web Animations API for better performance
  if ('animate' in element) {
    return element.animate(animation, {
      duration: 300,
      easing: 'ease-out',
      fill: 'forwards',
      ...options
    });
  }

  return null;
}

// Preload critical resources
export function preloadResource(href: string, as: string, type?: string, crossorigin?: boolean) {
  if (typeof document === 'undefined') return;

  // Check if already preloaded
  const existing = document.querySelector(`link[rel="preload"][href="${href}"]`);
  if (existing) return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  
  if (type) link.type = type;
  if (crossorigin) link.crossOrigin = 'anonymous';
  
  document.head.appendChild(link);
}

// Optimize images for better LCP
export function optimizeImageLoading() {
  if (typeof document === 'undefined') return;

  // Add loading="lazy" to images below the fold
  const images = document.querySelectorAll('img:not([loading])');
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        if (!img.loading) {
          img.loading = 'lazy';
        }
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach((img) => {
    imageObserver.observe(img);
  });
}

// Reduce Cumulative Layout Shift (CLS)
export function preventLayoutShift() {
  if (typeof document === 'undefined') return;

  // Add dimensions to images without them
  const images = document.querySelectorAll('img:not([width]):not([height])');
  images.forEach((img) => {
    const htmlImg = img as HTMLImageElement;
    if (htmlImg.naturalWidth && htmlImg.naturalHeight) {
      htmlImg.width = htmlImg.naturalWidth;
      htmlImg.height = htmlImg.naturalHeight;
    }
  });

  // Reserve space for dynamic content
  const dynamicElements = document.querySelectorAll('[data-dynamic-height]');
  dynamicElements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    const minHeight = htmlElement.dataset.dynamicHeight;
    if (minHeight) {
      htmlElement.style.minHeight = minHeight;
    }
  });
}

// Optimize font loading to reduce CLS
export function optimizeFontLoading() {
  if (typeof document === 'undefined') return;

  // Add font-display: swap to all font-face declarations
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
}

// Measure and report Core Web Vitals
export function measureWebVitals() {
  if (typeof window === 'undefined' || !window.PerformanceObserver) return;

  // Measure LCP
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    }).observe({ type: 'largest-contentful-paint', buffered: true });
  } catch (e) {
    // LCP not supported
  }

  // Measure FID
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime);
      });
    }).observe({ type: 'first-input', buffered: true });
  } catch (e) {
    // FID not supported
  }

  // Measure CLS
  try {
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      console.log('CLS:', clsValue);
    }).observe({ type: 'layout-shift', buffered: true });
  } catch (e) {
    // CLS not supported
  }
}

// Optimize critical rendering path
export function optimizeCriticalRenderingPath() {
  if (typeof document === 'undefined') return;

  // Inline critical CSS
  const criticalCSS = `
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
    .loading { opacity: 0; }
    .loaded { opacity: 1; transition: opacity 0.3s ease; }
  `;
  
  const style = document.createElement('style');
  style.textContent = criticalCSS;
  document.head.insertBefore(style, document.head.firstChild);

  // Mark page as loaded
  window.addEventListener('load', () => {
    document.body.classList.add('loaded');
  });
}

// Optimize third-party scripts
export function optimizeThirdPartyScripts() {
  if (typeof document === 'undefined') return;

  // Defer non-critical scripts
  const scripts = document.querySelectorAll('script[src]:not([async]):not([defer])');
  scripts.forEach((script) => {
    const htmlScript = script as HTMLScriptElement;
    if (!htmlScript.src.includes('critical')) {
      htmlScript.defer = true;
    }
  });
}

// Initialize all performance optimizations
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return;

  // Run optimizations when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      optimizeImageLoading();
      preventLayoutShift();
      optimizeFontLoading();
      optimizeCriticalRenderingPath();
      optimizeThirdPartyScripts();
    });
  } else {
    optimizeImageLoading();
    preventLayoutShift();
    optimizeFontLoading();
    optimizeCriticalRenderingPath();
    optimizeThirdPartyScripts();
  }

  // Measure performance in production
  if (process.env.NODE_ENV === 'production') {
    measureWebVitals();
  }
}

export default {
  debounce,
  throttle,
  optimizeAnimation,
  preloadResource,
  optimizeImageLoading,
  preventLayoutShift,
  optimizeFontLoading,
  measureWebVitals,
  optimizeCriticalRenderingPath,
  optimizeThirdPartyScripts,
  initializePerformanceOptimizations
};
