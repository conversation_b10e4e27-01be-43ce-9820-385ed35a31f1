import React, { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  priority?: boolean;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Optimized Image component with lazy loading, WebP support, and performance optimizations
 * Improves LCP and reduces CLS by providing proper dimensions and loading states
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  placeholder,
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Generate WebP version of the image if possible
  const getOptimizedSrc = (originalSrc: string): string => {
    // Check if it's already a WebP image
    if (originalSrc.includes('.webp')) {
      return originalSrc;
    }
    
    // For local images, try to use WebP version
    if (originalSrc.startsWith('/') || originalSrc.startsWith('./')) {
      const webpSrc = originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
      return webpSrc;
    }
    
    return originalSrc;
  };

  // Generate responsive srcSet for better performance
  const generateSrcSet = (baseSrc: string): string => {
    if (!baseSrc.startsWith('/') && !baseSrc.startsWith('./')) {
      return baseSrc; // External images, return as-is
    }

    const ext = baseSrc.split('.').pop();
    const baseName = baseSrc.replace(`.${ext}`, '');
    
    // Generate different sizes for responsive images
    return [
      `${baseName}-400w.${ext} 400w`,
      `${baseName}-800w.${ext} 800w`,
      `${baseName}-1200w.${ext} 1200w`,
      `${baseSrc} 1600w`
    ].join(', ');
  };

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) {
      // Load immediately for priority images
      setCurrentSrc(getOptimizedSrc(src));
      return;
    }

    if (!imgRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setCurrentSrc(getOptimizedSrc(src));
          observerRef.current?.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the image enters viewport
        threshold: 0.1
      }
    );

    observerRef.current.observe(imgRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [src, priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    // Fallback to original src if WebP fails
    if (currentSrc !== src) {
      setCurrentSrc(src);
      setHasError(false);
    } else {
      onError?.();
    }
  };

  // Calculate aspect ratio for CLS prevention
  const aspectRatio = width && height ? (height / width) * 100 : undefined;

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : 'auto',
        paddingBottom: aspectRatio ? `${aspectRatio}%` : undefined
      }}
    >
      {/* Placeholder/Loading state */}
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
          style={{
            backgroundColor: placeholder || 'rgba(255, 255, 255, 0.1)'
          }}
        >
          {!currentSrc && (
            <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
          )}
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-300 flex items-center justify-center text-gray-600">
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        </div>
      )}

      {/* Actual image */}
      {currentSrc && !hasError && (
        <img
          ref={imgRef}
          src={currentSrc}
          srcSet={generateSrcSet(currentSrc)}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={handleLoad}
          onError={handleError}
          className={`
            absolute inset-0 w-full h-full object-cover transition-opacity duration-300
            ${isLoaded ? 'opacity-100' : 'opacity-0'}
          `}
          style={{
            position: aspectRatio ? 'absolute' : 'static'
          }}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
